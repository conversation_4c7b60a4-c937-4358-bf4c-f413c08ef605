#!/usr/bin/env python3

import pandas as pd
import os

def test_event_mapping():
    """Test the event mapping logic"""
    
    print("Testing IPPEVENTS CSV mapping logic...")
    
    # Load CSV files
    base_dir = os.path.abspath("app/v2/data_sources/ncdr")
    elements_file = os.path.join(base_dir, "elements.csv")
    selections_file = os.path.join(base_dir, "selections.csv")
    
    try:
        elements_df = pd.read_csv(elements_file, encoding='utf-8')
    except UnicodeDecodeError:
        elements_df = pd.read_csv(elements_file, encoding='latin1')
    
    try:
        selections_df = pd.read_csv(selections_file, encoding='utf-8')
    except UnicodeDecodeError:
        selections_df = pd.read_csv(selections_file, encoding='latin1')
    
    print(f"Loaded {len(elements_df)} elements and {len(selections_df)} selections")
    
    # Test field ID 12153 lookup
    element_12153 = elements_df[elements_df['Element Reference'] == 12153]
    if not element_12153.empty:
        element_12153_row = element_12153.iloc[0]
        element_code = str(element_12153_row['NCDR Code']).strip()
        element_code_system = str(element_12153_row['Code System']).strip()
        element_display_name = str(element_12153_row['Name']).strip()
        
        print(f"\nField ID 12153 (Event Element):")
        print(f"  Element code: {element_code}")
        print(f"  Code system: {element_code_system}")
        print(f"  Display name: {element_display_name}")
    else:
        print("ERROR: Could not find element for field ID 12153")
        return
    
    # Test field ID 9002 lookup
    element_9002 = elements_df[elements_df['Element Reference'] == 9002]
    if not element_9002.empty:
        element_9002_row = element_9002.iloc[0]
        occurred_code = str(element_9002_row['NCDR Code']).strip()
        occurred_code_system = str(element_9002_row['Code System']).strip()
        occurred_display_name = str(element_9002_row['Name']).strip()
        
        print(f"\nField ID 9002 (Event Occurred):")
        print(f"  Element code: {occurred_code}")
        print(f"  Code system: {occurred_code_system}")
        print(f"  Display name: {occurred_display_name}")
    else:
        print("ERROR: Could not find element for field ID 9002")
        return
    
    # Test selections lookup for field ID 12153
    event_selections = selections_df[selections_df['Element Reference'] == 12153]
    print(f"\nFound {len(event_selections)} selections for field ID 12153:")
    
    # Test specific events
    test_events = ['Air Embolism', 'Cardiac Arrest', 'Heart Failure', 'GI Bleeding']
    
    for event_name in test_events:
        print(f"\nTesting event: {event_name}")
        
        # Look for exact match first
        matching_selection = event_selections[
            event_selections['Selection Name'].str.strip() == event_name
        ]
        
        if not matching_selection.empty:
            selection_row = matching_selection.iloc[0]
            selection_code = str(selection_row['Code']).strip()
            selection_code_system = str(selection_row['Code System']).strip()
            selection_display_name = str(selection_row['Selection Name']).strip()
            
            print(f"  ✓ Found exact match:")
            print(f"    Selection code: {selection_code}")
            print(f"    Code system: {selection_code_system}")
            print(f"    Display name: {selection_display_name}")
            
            # Generate expected XML
            print(f"  Expected XML:")
            print(f'    <element code="{element_code}" codeSystem="{element_code_system}" displayName="{element_display_name}">')
            print(f'      <value xsi:type="CD" code="{selection_code}" codeSystem="{selection_code_system}" displayName="{selection_display_name}" />')
            print(f'    </element>')
            print(f'    <element code="{occurred_code}" codeSystem="{occurred_code_system}" displayName="Event Occurred">')
            print(f'      <value xsi:type="BL" value="true" />')
            print(f'    </element>')
            
        else:
            # Try partial match
            matching_selection = event_selections[
                event_selections['Selection Name'].str.contains(event_name, case=False, na=False)
            ]
            if not matching_selection.empty:
                selection_row = matching_selection.iloc[0]
                selection_code = str(selection_row['Code']).strip()
                selection_display_name = str(selection_row['Selection Name']).strip()
                print(f"  ⚠ Found partial match: {selection_display_name} (code: {selection_code})")
            else:
                print(f"  ✗ No match found for '{event_name}'")
                # Show available options
                print("    Available selections:")
                for idx, row in event_selections.head(10).iterrows():
                    print(f"      - {row['Selection Name']} (code: {row['Code']})")
                if len(event_selections) > 10:
                    print(f"      ... and {len(event_selections) - 10} more")

def test_current_xml_issues():
    """Analyze the current XML issues"""
    
    print("\n" + "="*60)
    print("ANALYZING CURRENT XML ISSUES")
    print("="*60)
    
    # Load selections to check what code 74474003 corresponds to
    base_dir = os.path.abspath("app/v2/data_sources/ncdr")
    selections_file = os.path.join(base_dir, "selections.csv")
    
    try:
        selections_df = pd.read_csv(selections_file, encoding='utf-8')
    except UnicodeDecodeError:
        selections_df = pd.read_csv(selections_file, encoding='latin1')
    
    # Check what code 74474003 is
    code_74474003 = selections_df[selections_df['Code'] == 74474003]
    if not code_74474003.empty:
        row = code_74474003.iloc[0]
        print(f"Code 74474003 corresponds to: {row['Selection Name']} (Element Reference: {row['Element Reference']})")
    else:
        print("Code 74474003 not found in selections")
    
    # Check what code 271376002 is (expected for Air Embolism)
    code_271376002 = selections_df[selections_df['Code'] == 271376002]
    if not code_271376002.empty:
        row = code_271376002.iloc[0]
        print(f"Code 271376002 corresponds to: {row['Selection Name']} (Element Reference: {row['Element Reference']})")
    else:
        print("Code 271376002 not found in selections")

if __name__ == "__main__":
    test_event_mapping()
    test_current_xml_issues()
