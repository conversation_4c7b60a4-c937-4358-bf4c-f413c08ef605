#!/usr/bin/env python3

import sys
import os
import logging

# Add the app directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s:%(lineno)d - %(message)s')

def test_ippevents_processing():
    """Test the complete IPPEVENTS processing"""
    
    try:
        from v2.utils.ncdr_registry_utils import process_intra_post_procedure_events
        import xml.etree.ElementTree as ET
        
        print("Testing complete IPPEVENTS processing...")
        
        # Load the existing XML file to see current state
        xml_file = "app/v2/data_sources/xml/683824d697f6c899fa83e18f/LAAO999996-2025Q1.xml"
        if os.path.exists(xml_file):
            print(f"Loading existing XML: {xml_file}")
            tree = ET.parse(xml_file)
            
            # Check current IPPEVENTS sections
            existing_sections = tree.findall(".//section[@code='IPPEVENTS']")
            print(f"Current XML has {len(existing_sections)} IPPEVENTS sections")
            
            for i, section in enumerate(existing_sections):
                print(f"Section {i+1}:")
                elements = section.findall("element")
                for j, element in enumerate(elements):
                    print(f"  Element {j+1}: {element.get('displayName')} (code: {element.get('code')})")
                    value = element.find("value")
                    if value is not None:
                        if value.get('value'):
                            print(f"    Value: {value.get('value')}")
                        else:
                            print(f"    Value: {value.get('displayName')} (code: {value.get('code')})")
            
            # Create test event data
            event_values = {
                'IPPEVENTS': {
                    'air_embolism': {
                        'value': 'No',
                        'field_id': '12153',
                        'date': ''
                    },
                    'cardiac_arrest': {
                        'value': 'Yes', 
                        'field_id': '12153',
                        'date': '2023-01-02'
                    },
                    'heart_failure': {
                        'value': 'Yes',
                        'field_id': '12153', 
                        'date': '2023-01-02'
                    }
                }
            }
            
            print("\nTest event values:")
            for event_name, event_info in event_values['IPPEVENTS'].items():
                print(f"  {event_name}: {event_info}")
            
            print("\nCalling process_intra_post_procedure_events...")
            process_intra_post_procedure_events(tree, event_values)
            
            # Check the results
            new_sections = tree.findall(".//section[@code='IPPEVENTS']")
            print(f"\nAfter processing: {len(new_sections)} IPPEVENTS sections")
            
            for i, section in enumerate(new_sections):
                print(f"New Section {i+1}:")
                elements = section.findall("element")
                for j, element in enumerate(elements):
                    print(f"  Element {j+1}: {element.get('displayName')} (code: {element.get('code')})")
                    value = element.find("value")
                    if value is not None:
                        if value.get('value'):
                            print(f"    Value: {value.get('value')}")
                        else:
                            print(f"    Value: {value.get('displayName')} (code: {value.get('code')})")
            
            # Save the updated XML for inspection
            output_file = "test_output.xml"
            tree.write(output_file, encoding="utf-8", xml_declaration=True)
            print(f"\nSaved updated XML to: {output_file}")
            
        else:
            print(f"XML file not found: {xml_file}")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ippevents_processing()
